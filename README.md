# Brookland Auto Detailing

## Налаштування інтеграції з Telegram

Для того, щоб форма відправляла повідомлення в Telegram, вам потрібно:

1. **Створити бота в Telegram**:
   - Відкрийте Telegram і знайдіть BotFather (@BotFather)
   - Надішліть команду `/newbot`
   - Слідуйте інструкціям для створення бота
   - Після створення, скопіюйте токен бота (наприклад: `123456789:ABCdefGhIJKlmnOPQrsTUVwxyZ`)

2. **Отримати ID чату або каналу**:
   - Якщо ви хочете отримувати повідомлення в особистий чат:
     - Знайдіть бота @userinfobot і надішліть йому будь-яке повідомлення
     - Він відповість вашим ID (наприклад: `12345678`)
   - Якщо ви хочете отримувати повідомлення в канал/групу:
     - Додайте вашого бота до каналу/групи як адміністратора
     - Надішліть повідомлення в канал/групу
     - Відкрийте URL: `https://api.telegram.org/bot<ВАШ_ТОКЕН>/getUpdates`
     - Знайдіть `"chat":{"id":-10012345678}` (ID каналу/групи зазвичай від'ємне і починається з -100)

3. **Оновіть налаштування в файлі script.js**:
   - Знайдіть функцію `sendToTelegram` у файлі `script.js`
   - Замініть значення `YOUR_BOT_TOKEN` на ваш токен бота
   - Замініть значення `YOUR_CHAT_ID` на ID вашого чату або каналу

```javascript
// Приклад:
const BOT_TOKEN = '123456789:ABCdefGhIJKlmnOPQrsTUVwxyZ';
const CHAT_ID = '-1001234567890'; // або просто числове значення для особистого чату
```

## Безпека
Зверніть увагу, що токен бота дає повний доступ до вашого бота. В ідеалі слід використовувати серверну частину для відправки повідомлень, щоб не показувати токен у фронтенд коді. Для виробничого сайту рекомендується створити API на сервері, який буде приймати дані форми і відправляти їх у Telegram. 