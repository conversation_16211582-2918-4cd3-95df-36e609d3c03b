/* === HEADER === */
.wrapper {
    max-width: 1350px;
    margin: 0 auto;
    width: 100%;
}
header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    flex-wrap: wrap;
}
.menu {
    display: flex;
    list-style-type: none;
    padding: 0;
    gap: 10px;
    position: relative;
    transition: all 0s ease-in 0s;
}
.menu-item {
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 8px 12px;
    color: white;
}
.menu-item:hover {
    scale: 1.1;
}
.contacts {
    background-color: #000;
    padding: 15px 0;
}
.contacts__item a,
.contacts__item span {
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
}
.contacts__item a:hover {
    text-decoration: underline;
}
.social {
    padding: 25px 0;
    background-color: #000;
}
.social .wrapper {
    max-width: 180px;
}
.social__buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}
.social__button > img {
    width: 32px;
}
.burger-menu {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 20px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;
}
.burger-menu span {
    width: 100%;
    height: 2px;
    background-color: white;
    transition: all 0.3s ease;
}
.burger-menu.active span:first-child {
    transform: rotate(45deg) translate(6px, 6px);
}
.burger-menu.active span:nth-child(2) {
    opacity: 0;
}
.burger-menu.active span:last-child {
    transform: rotate(-45deg) translate(6px, -6px);
}
.burger-menu-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 999;
}

/* === BANNER === */
.banner {
    background-image: url('./images/Frame\ 15.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 150px 0;
    position: relative;
}
.banner__subtitle {
    font-family: 'Raleway', sans-serif;
    font-weight: 500;
    font-size: 14px;
    color: white;
    margin-bottom: 20px;
}
.banner__title {
    font-weight: 600;
    font-size: 48px;
    line-height: 1.2;
    color: white;
    margin-bottom: 40px;
}
.banner__buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}
.banner__content {
    margin: 0 auto;
}

/* === WORK PROCESS === */
.work-process {
    padding: 60px 0;
    background-color: #000;
    position: relative;
}
.work-process__title {
    font-size: 36px;
    font-weight: 600;
    color: white;
    text-align: center;
    margin-bottom: 40px;
}
.work-process__grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(2, 1fr);
    grid-column-gap: 20px;
    grid-row-gap: 20px;
}
.work-process__grid img {
    width: 100%;
}
.work-process__item-1 { grid-area: 1 / 1 / 2 / 2; }
.work-process__item-2 { grid-area: 2 / 1 / 3 / 2; }
.work-process__item-3 { grid-area: 1 / 2 / 2 / 3; }
.work-process__item-4 { grid-area: 2 / 2 / 3 / 3; }
.work-process__item-5 { grid-area: 1 / 3 / 3 / 5; }

.work-process__item-1,
.work-process__item-2,
.work-process__item-3,
.work-process__item-4,
.work-process__item-5 {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
}

.work-process__item-1 img,
.work-process__item-2 img,
.work-process__item-3 img,
.work-process__item-4 img,
.work-process__item-5 img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.work-process__flip {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.work-process__item-1:hover .work-process__flip,
.work-process__item-2:hover .work-process__flip,
.work-process__item-3:hover .work-process__flip,
.work-process__item-4:hover .work-process__flip,
.work-process__item-5:hover .work-process__flip {
    opacity: 1;
}

.work-process__item-1:hover img,
.work-process__item-2:hover img,
.work-process__item-3:hover img,
.work-process__item-4:hover img,
.work-process__item-5:hover img {
    transform: scale(1.1);
}

.work-process__instagram {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.work-process__instagram:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.work-process__instagram img {
    width: 24px;
    height: 24px;
}

.work-process__more {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.work-process__more-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.work-process__more-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.work-process__more-button img {
    width: 20px;
    height: 20px;
}

/* === SERVICES === */
.services {
    padding: 60px 0;
    background-color: #000;
}
.services__title {
    font-size: 32px;
    font-weight: 600;
    color: white;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}
.services .wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.services__grid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    gap: 20px;
}
.service-card {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    width: 370px;
    height: 370px;
    border-radius: 12px;
    overflow: hidden;
}
.hint-shake {
    animation: hintShake 1s ease-in-out 1s 1;
}
.service-card__side {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-size: 20px;
    color: white;
    text-align: center;
    border-radius: 12px;
    overflow: hidden;
}
.service-card__front {
    background: rgba(0, 0, 0, 0.5);
    z-index: 2;
    flex-direction: column;
}
.service-card__back {
    opacity: 0;
    z-index: 2;
    flex-direction: column;
    gap: 20px;
}
.flipped {
    .service-card__back {
        opacity: 1;
    }
    .service-card__front {
        display: none;
    }
    .service-card__image {
    transform: scale(1.1);
    }
}
.service-card__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
    z-index: 0;
    transition: transform 0.3s ease;
}

@keyframes hintShake {
    0% { transform: rotateY(0); }
    30% { transform: rotateY(30deg); }
    60% { transform: rotateY(-30deg); }
    100% { transform: rotateY(0); }
}
.service-card__title {
    font-weight: 600;
    font-size: 18px;
    color: white;
    margin-bottom: 15px;
    min-height: 44px;
    z-index: 2;
}
.services__more {
    display: inline-block;
    color: white;
    text-decoration: none;
    font-family: 'Raleway', sans-serif;
    font-weight: 500;
    font-size: 14px;
    margin: 0 auto;
    padding: 10px 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    transition: all 0.3s ease;
}
.services__more:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* === SPECIAL OFFER === */
.special-offer {
    padding: 80px 0;
    background-color: #000;
    background-image: url('./images/special-offer-bg.png');
    background-size: contain;
    position: relative;
    background-repeat: no-repeat;
    background-position: center;
}
.special-offer .wrapper {
    position: relative;
    z-index: 2;
}
.special-offer__content {
    display: flex;
    gap: 35px;
    justify-content: center;
    align-items: center;
    text-align: left;
    margin: 0 auto;
}
.special-offer__title {
    font-size: 35px;
    color: white;
    margin-bottom: 20px;
}
.special-offer__discount {
    font-size: 72px;
    margin: 30px 0;
    text-wrap: nowrap;
}
.special-offer__subtitle {
    font-size: 36px;
    margin-bottom: 40px;
}
.special-offer__button {
    background-color: #B22222;
    text-decoration: none;
    padding: 15px 40px;
    border-radius: 5px;
    font-size: 16px;
    color: white;
    height: 55px;
}
.special-offer__button:hover {
    background-color: #8B0000;
}

/* === ACHIEVEMENTS === */
#achievements {
    margin-top: 30px;
    padding: 20px;
    background-image: url('./images/achievements-bg.png');
    background-position: center;
}
.achievements-container {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}
.achievements-title {
    font-size: 36px;
    line-height: 40px;
    margin-bottom: 10px;
}
.achievements-subtitle {
    font-size: 20px;
    line-height: 150%;
    font-weight: 400;
    margin-bottom: 80px;
}
.achievement__card {
    background: #f12832;
    height: 190px;
    width: 305px;
    padding: 24px;
    text-align: start;
    border-radius: 15px;
}
.achievement__card_title {
    font-size: 22px;
    line-height: 16px;
}
.achievement__card_number {
    margin-top: 15px;
    font-size: 48px;
    line-height: 36px;
    font-weight: 600;
}
.achievement__card_description {
    margin-top: 30px;
    font-size: 18px;
    font-weight: 400;
}

/* === TESTIMONIALS === */
.testimonials {
    padding: 80px 0;
    background-color: #000;
}
.testimonials .wrapper {
    flex-direction: column;
}
.testimonials__title {
    font-size: 36px;
    font-weight: 600;
    color: white;
    text-align: center;
    margin-bottom: 50px;
}
.testimonials__title span {
    color: #B22222;
}
.testimonials__slider {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    width: 100%;
}
.testimonials__slides {
    flex: 1;
    position: relative;
    height: 400px;
    overflow: hidden;
}
.testimonial-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 30px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease;
}
.testimonial-slide.active {
    opacity: 1;
    transform: translateX(0);
}
.testimonial-slide__header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}
.testimonial-slide__avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}
.testimonial-slide__name {
    font-family: 'Anybody', sans-serif;
    font-size: 20px;
    color: white;
    margin-bottom: 5px;
}
.testimonial-slide__rating {
    color: #FFD700;
    font-size: 18px;
}
.testimonial-slide__text {
    font-family: 'Raleway', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
}
.testimonial-slide img {
    max-height: 400px;
    margin: 0 auto;
}
.slider-button {
    width: 50px;
    height: 50px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}
.slider-button:hover {
    background: rgba(255, 255, 255, 0.2);
}
.slider-button img {
    width: 24px;
    height: 24px;
}
.testimonials__dots {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}
.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: none;
    background-color: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: background-color 0.3s ease;
}
.dot.active {
    background-color: #B22222;
}
.dot:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

/* === FAQ === */
.faq {
    padding: 80px 0;
}
.faq__title {
    font-size: 36px;
    font-weight: 600;
    color: white;
    text-align: center;
    margin-bottom: 50px;
}
.accordion {
    max-width: 1000px;
    margin: 0 auto;
}
.accordion__item {
    margin-bottom: 15px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.695);
}
.accordion__button {
    width: 100%;
    padding: 20px;
    background: transparent;
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}
.accordion__button span {
    font-size: 18px;
    color: white;
    text-align: left;
}
.accordion__button img {
    width: 24px;
    height: 24px;
    transition: transform 0.3s ease;
}
.accordion__button.active img {
    transform: rotate(180deg);
}
.accordion__content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    padding: 0 20px;
}
.accordion__content p {
    font-family: 'Raleway', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    padding-bottom: 20px;
}
.accordion__button:hover {
    background: rgba(255, 255, 255, 0.05);
}
.accordion__item:hover {
    border-color: rgba(255, 255, 255, 0.2);
}

/* === ORDER (FORM) === */
.order {
    margin: 0 auto;
    padding: 25px;
    max-width: 600px;
    position: relative;
}
.form-group {
    margin-bottom: 20px;
}
.form-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    text-align: left;
}
.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}
.form-group input:invalid {
    border-color: #B22222;
}
.modal-submit {
    width: 100%;
    padding: 12px;
    background-color: #9e2b25;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}
.modal-submit:hover {
    background-color: #7a1f1c;
}

/* === LOCATION === */
.location {
    padding: 80px 0;
    background-color: #000;
}
.location .wrapper {
    flex-direction: column;
}
.location__title {
    font-size: 36px;
    font-weight: 600;
    color: white;
    text-align: center;
    margin-bottom: 50px;
}
.location__content {
    display: flex;
    gap: 40px;
    align-items: flex-start;
    width: 100%;
}
.location__info {
    flex: 0 0 350px;
    display: flex;
    flex-direction: column;
    gap: 30px;
}
.location__item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 25px;
    background: linear-gradient(180deg, rgba(50, 50, 50, 0.4) 0%, rgba(30, 30, 30, 0.4) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transition: transform 0.3s ease;
}
.location__item:hover {
    transform: translateY(-5px);
}
.location__item img {
    width: 24px;
    height: 24px;
}
.location__text h3 {
    font-family: 'Anybody', sans-serif;
    font-size: 18px;
    font-weight: 600;
    color: #B22222;
    margin-bottom: 8px;
    text-align: left;
}
.location__text p,
.location__text a {
    font-family: 'Raleway', sans-serif;
    font-size: 16px;
    color: white;
    text-decoration: none;
    line-height: 1.4;
}
.location__text a:hover {
    text-decoration: underline;
}
.location__map {
    flex: 1;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    min-width: 0;
}
.location__map iframe {
    display: block;
    width: 100%;
    height: 450px;
    border: none;
}

/* === FOOTER === */
.footer {
    background-color: #000;
    padding: 25px 0;
    margin-top: 50px;
}
.footer .wrapper {
    justify-content: center;
}
.privacy-policy {
    color: white;
    text-decoration: none;
    font-size: 14px;
    padding: 8px 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    transition: background-color 0.3s, border-color 0.3s;
}
.privacy-policy:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* === SCROLL TOP === */
.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: #B22222;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}
.scroll-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}
.scroll-top:hover {
    background: #8B0000;
    transform: translateY(-3px);
}
.scroll-top img {
    width: 24px;
    height: 24px;
}

.button {
    background-color: #B22222;
    color: white;
    padding: 10px 30px;
    border-radius: 5px;
    font-weight: 500;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
}
.button--outlined {
    background-color: rgba(20, 20, 20, 0.5);
    border: 2px solid #B22222;
}

@media (max-width: 1200px) {
    .wrapper {
        max-width: 960px;
    }
    .services__grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .work-process__grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
@media (max-width: 992px) {
    .wrapper {
        max-width: 720px;
    }
    .banner__title {
        font-size: 36px;
    }
    .special-offer__title {
        font-size: 32px;
    }
    .special-offer__discount {
        font-size: 56px;
    }
    .location__content {
        flex-direction: column;
    }
    .location__info {
        flex: none;
        width: 100%;
    }
    .burger-menu {
        display: flex;
    }
    .nav {
        position: fixed;
        top: 0;
        left: -100%;
        width: 300px;
        height: 100%;
        background-color: #000;
        padding: 100px 30px 30px;
        transition: left 0.3s ease;
        z-index: 1000;
    }
    .nav.active {
        left: 0;
    }
    .menu {
        flex-direction: column;
        background: none;
        padding: 0;
    }
    header .wrapper {
        justify-content: center;
        position: relative;
    }
    .burger-menu {
        position: absolute;
        left: 15px;
    }
    .service-card {
        width: 45%;
        aspect-ratio: 1 / 1;
    }
}
@media (max-width: 768px) {
    .wrapper {
        max-width: 540px;
    }
    .main__logo-container {
        width: -webkit-fill-available;
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }
    .services__grid {
        grid-template-columns: 1fr;
    }
    .work-process__grid {
        grid-template-columns: 1fr;
    }
    .banner .wrapper {
        flex-direction: column;
    }
    .banner .wrapper > div:last-child {
        width: 100%;
    }
    .banner .wrapper > div:last-child img {
        max-width: 100%;
        animation: fade-in 1s forwards;
        transform: translateX(0);
    }
    @keyframes fade-in {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    .banner__title {
        font-size: 24px;
    }
    .banner__buttons .button {
        margin: 5px 0;
    }
    .special-offer__content {
        flex-direction: column;
    }
    .special-offer__title {
        font-size: 28px;
    }
    .special-offer__discount {
        font-size: 48px;
    }
    .testimonial-slide {
        padding: 20px;
    }
    .testimonial-slide__text {
        font-size: 14px;
    }
    .modal-content {
        margin: 15% auto;
        width: 90%;
    }
}
@media (max-width: 576px) {
    .wrapper {
        max-width: 100%;
        padding: 0 15px;
    }
    .main__logo {
        padding: 30px 0;
        width: 200px;
        height: 40px;
    }
    .banner__content {
        padding: 50px 0 ;
    }
    .work-process__title {
        font-size: 24px;
    }
    .burger-menu {
        top: 120px;
    }
    .service-card {
        height: 150px;
        align-items: center;
    }
    .service-card__side {
        align-items: center;
    }
    .service-card__title {
        align-content: center;
        margin: 0;
    }
    .work-process__grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(4, auto); /* адаптивно по висоті */
    }

    /* Перепризначення сітки для малих екранів */
    .work-process__item-1 { grid-area: 1 / 1 / 2 / 2; }
    .work-process__item-2 { grid-area: 1 / 2 / 2 / 3; }
    .work-process__item-5 { grid-area: 2 / 1 / 4 / 3; } /* велика картинка */
    .work-process__item-3 { grid-area: 4 / 1 / 5 / 2; }
    .work-process__item-4 { grid-area: 4 / 2 / 5 / 3; }
    .social__button {
        width: 25px;
    }
    .achievements {
        padding: 0;
    }
    .testimonial-card {
        width: 70vw;
    }
    .achievement__card {
        padding: 15px;
width: 45%;
height: 250px;
    }
    .services {
        padding: 30px 0;
    }
    .contacts .wrapper {
        flex-direction: column;
        gap: 15px;
    }
    .contacts {
        margin-left: 50px;
    }
    .banner__title {
        font-size: 22px;
    }
    .services__title {
        font-size: 22px;
    }
    .special-offer__title {
        font-size: 24px;
    }
    .service-card__title {
        font-weight: 400;
    }
    .special-offer__discount {
        font-size: 42px;
    }
    .accordion__button span {
        font-size: 16px;
    }
    .location__item {
        padding: 15px;
    }
    .scroll-top {
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
    }
}
@media (max-width: 600px) {
    .testimonials-scroll-container {
        gap: 4px;
    }
    .testimonial-card {
        padding: 10px 2px 8px 2px;
    }
    .testimonials-arrow {
        width: 32px;
        height: 32px;
    }
    .testimonials-cards-list {
        gap: 8px;
        padding-bottom: 6px;
    }
}
.testimonials-arrow:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
}

.testimonials-scroll-container {
    display: flex;
    align-items: flex-end;
    gap: 20px;
    width: 100%;
    position: relative;
}
.testimonials-cards-list {
    display: flex;
    gap: 24px;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding-bottom: 16px;
    scrollbar-width: thin;
    scrollbar-color: #B22222 #222;
}
.testimonials-cards-list::-webkit-scrollbar {
    height: 8px;
}
.testimonials-cards-list::-webkit-scrollbar-thumb {
    background: #B22222;
    border-radius: 4px;
}
.testimonial-card {
    min-width: 320px;
    max-width: 340px;
    background: #232323;
    border-radius: 16px;
    box-shadow: 0 2px 16px 0 rgba(0,0,0,0.15);
    padding: 24px 20px 16px 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border: 1px solid rgba(255,255,255,0.08);
    color: #fff;
    flex-shrink: 0;
    position: relative;
}
.testimonial-card__stars {
    display: flex;
    align-items: center;
    gap: 2px;
    margin-bottom: 12px;
}
.testimonial-card__rating {
    margin-left: 8px;
    color: #fff;
    font-weight: 600;
    font-size: 16px;
}
.testimonial-card__text {
    font-size: 15px;
    color: #e0e0e0;
    margin-bottom: 24px;
    min-height: 80px;
}
.testimonial-card__footer {
    display: flex;
    align-items: center;
    gap: 12px;
    border-top: 1px solid rgba(255,255,255,0.08);
    padding-top: 12px;
}
.testimonial-card__avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #B22222;
}
.testimonial-card__name {
    font-size: 15px;
    font-weight: 600;
    color: #fff;
}
.testimonials-arrow {
    background: #232323;
    border: 1px solid #B22222;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.2s, border 0.2s;
    position: relative;
    z-index: 2;
}
.testimonials-arrow:hover {
    background: #B22222;
    border-color: #fff;
}
@media (max-width: 900px) {
    .testimonial-card {

        padding: 16px 10px 10px 10px;
    }
}
@media (max-width: 600px) {
    .testimonials-scroll-container {
        gap: 8px;
    }
    .testimonial-card {

        padding: 12px 6px 8px 6px;
    }
}
