document.addEventListener("DOMContentLoaded", function () {
    initMainAccordion();
});

const burgerMenu = document.querySelector(".burger-menu");
const nav = document.querySelector(".nav");
const overlay = document.querySelector(".burger-menu-overlay");
const menuItems = document.querySelectorAll(".menuItem");

function toggleMenu() {
    burgerMenu.classList.toggle("active");
    nav.classList.toggle("active");

    if (nav.classList.contains("active")) {
        overlay.style.display = "block";
        document.body.style.overflow = "hidden";
    } else {
        overlay.style.display = "none";
        document.body.style.overflow = "";
    }
}

burgerMenu.addEventListener("click", toggleMenu);
overlay.addEventListener("click", toggleMenu);

menuItems.forEach((item) => {
    item.addEventListener("click", () => {
        if (nav.classList.contains("active")) {
            toggleMenu();
        }
    });
});

window.addEventListener("resize", () => {
    if (window.innerWidth > 992 && nav.classList.contains("active")) {
        toggleMenu();
    }
});

document.addEventListener("DOMContentLoaded", function () {
    const scrollContainer = document.querySelector(".testimonials-cards-list");
    const leftArrow = document.querySelector(".testimonials-arrow--left");
    const rightArrow = document.querySelector(".testimonials-arrow--right");
    function updateArrows() {
        if (!scrollContainer) return;
        leftArrow.disabled = scrollContainer.scrollLeft <= 10;
        rightArrow.disabled =
            scrollContainer.scrollLeft + scrollContainer.offsetWidth >= scrollContainer.scrollWidth - 10;
    }
    if (scrollContainer && leftArrow && rightArrow) {
        const scrollCard = () => {
            const card = scrollContainer.querySelector(".testimonial-card");
            if (!card) return 320;
            const style = window.getComputedStyle(card);
            const gap = parseInt(style.marginRight || 24) || 24;
            return card.offsetWidth + gap;
        };
        leftArrow.addEventListener("click", () => {
            scrollContainer.scrollBy({ left: -scrollCard(), behavior: "smooth" });
            setTimeout(updateArrows, 400);
        });
        rightArrow.addEventListener("click", () => {
            scrollContainer.scrollBy({ left: scrollCard(), behavior: "smooth" });
            setTimeout(updateArrows, 400);
        });
        scrollContainer.addEventListener("scroll", updateArrows);
        window.addEventListener("resize", updateArrows);
        setTimeout(updateArrows, 200);
    }
});

const serviceButtons = document.querySelectorAll(".service-card__button");
const serviceForm = document.getElementById("serviceForm");
const nameInput = document.getElementById("name");
const phoneInput = document.getElementById("phone");
const commentInput = document.getElementById("comment");

if (nameInput && phoneInput) {
    nameInput.setAttribute("required", "");
    phoneInput.setAttribute("required", "");
    phoneInput.pattern = "^[+]?[0-9]{10,13}$";

    phoneInput.addEventListener("input", function () {
        if (this.validity.patternMismatch) {
            this.setCustomValidity("Будь ласка, введіть коректний номер телефону");
        } else {
            this.setCustomValidity("");
        }
    });
}

if (serviceForm && nameInput && phoneInput) {
    serviceForm.addEventListener("submit", function (e) {
        e.preventDefault();
        if (!this.checkValidity()) return;
        const name = nameInput.value.trim();
        const phone = phoneInput.value.trim();
        const comment = commentInput.value.trim();
        sendToTelegram(name, phone, comment)
            .then((response) => {
                if (response.ok) {
                    alert("Дякуємо за заявку! Ми зв'яжемося з вами найближчим часом.");
                } else {
                    alert(
                        "Виникла помилка при відправці заявки. Будь ласка, спробуйте ще раз або зв'яжіться з нами за телефоном."
                    );
                }
            })
            .catch((error) => {
                console.error("Error sending message to Telegram:", error);
                alert(
                    "Виникла помилка при відправці заявки. Будь ласка, спробуйте ще раз або зв'яжіться з нами за телефоном."
                );
            });
    });
}

async function sendToTelegram(name, phone, comment) {
    const BOT_TOKEN = "YOUR_BOT_TOKEN";
    const CHAT_ID = "YOUR_CHAT_ID";
    const text = `📩 Нова заявка!\n\n▪️Ім'я: ${name}\n▪️ Телефон: ${phone}\n▪️ Коментар: ${comment}`;
    const url = `https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`;
    console.log(text);

    const params = {
        chat_id: CHAT_ID,
        text: text,
        parse_mode: "HTML",
    };
    return fetch(url, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
    });
}

const specialOfferButton = document.querySelector(".special-offer__button");
if (specialOfferButton && serviceForm) {
    specialOfferButton.addEventListener("click", function (e) {
        e.preventDefault();
        serviceForm.dataset.service = "Спеціальна пропозиція";
        document.body.style.overflow = "hidden";
    });
}

const appointmentButtons = document.querySelectorAll(".button--primary");
appointmentButtons.forEach((button) => {
    if (button.textContent.trim() === "Записатись" && serviceForm) {
        button.addEventListener("click", function (e) {
            e.preventDefault();
            window.location.href = "index.html#order";
        });
    }
});

// FAQ-акордеон для головної сторінки
function initMainAccordion() {
    const accordionButtons = document.querySelectorAll(".accordion__button");
    if (accordionButtons.length) {
        accordionButtons.forEach((button) => {
            button.addEventListener("click", () => {
                const accordionContent = button.nextElementSibling;
                const isActive = button.classList.contains("active");

                // Закриваємо всі інші акордеони
                accordionButtons.forEach((otherButton) => {
                    if (otherButton !== button) {
                        otherButton.classList.remove("active");
                        otherButton.nextElementSibling.style.maxHeight = "0";
                    }
                });

                // Перемикаємо поточний акордеон
                button.classList.toggle("active");
                if (!isActive) {
                    accordionContent.style.maxHeight = accordionContent.scrollHeight + "px";
                } else {
                    accordionContent.style.maxHeight = "0";
                }
            });
        });
    }
}

function isTouchDevice() {
    return "ontouchstart" in window || navigator.maxTouchPoints > 0;
}

document.querySelectorAll(".service-card").forEach((card) => {
    let timeoutId;

    card.addEventListener("mouseenter", () => {
        if (isTouchDevice()) return;
        clearTimeout(timeoutId);
        card.classList.add("flipped");
    });

    card.addEventListener("mouseleave", () => {
        if (isTouchDevice()) return;
        timeoutId = setTimeout(() => {
            card.classList.remove("flipped");
        }, 300);
    });

    card.addEventListener("click", (e) => {
        if (!isTouchDevice()) return;
        if (!card.classList.contains("flipped")) {
            e.preventDefault();
        }
        card.classList.toggle("flipped");
        timeoutId = setTimeout(() => {
            card.classList.remove("flipped");
        }, 3000);
    });
});
