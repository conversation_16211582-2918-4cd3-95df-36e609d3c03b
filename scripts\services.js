// Динамічне підключення хедера
function loadHeader() {
    fetch("../header.html")
        .then((response) => {
            if (!response.ok) throw new Error("not found");
            return response.text();
        })
        .then((data) => {
            document.getElementById("header-placeholder").innerHTML = data;
            initHeaderScripts();
        })
        .catch(() => {
            fetch("header.html")
                .then((response) => response.text())
                .then((data) => {
                    document.getElementById("header-placeholder").innerHTML = data;
                    initHeaderScripts();
                });
        });
}

function initHeaderScripts() {
    const burgerMenu = document.querySelector(".burger-menu");
    const nav = document.querySelector(".nav");
    const overlay = document.querySelector(".burger-menu-overlay");
    const menuItems = document.querySelectorAll(".menu-item");

    if (!burgerMenu || !nav || !overlay) return;

    function toggleMenu() {
        burgerMenu.classList.toggle("active");
        nav.classList.toggle("active");

        if (nav.classList.contains("active")) {
            overlay.style.display = "block";
            document.body.style.overflow = "hidden";
        } else {
            overlay.style.display = "none";
            document.body.style.overflow = "";
        }
    }

    burgerMenu.addEventListener("click", toggleMenu);
    overlay.addEventListener("click", toggleMenu);

    menuItems.forEach((item) => {
        item.addEventListener("click", () => {
            if (nav.classList.contains("active")) {
                toggleMenu();
            }
        });
    });

    window.addEventListener("resize", () => {
        if (window.innerWidth > 992 && nav.classList.contains("active")) {
            toggleMenu();
        }
    });
}

document.addEventListener("DOMContentLoaded", function () {
    if (document.getElementById("header-placeholder")) {
        loadHeader();
    }
    initAccordion();
});

// FAQ-акордеон
function initAccordion() {
    const accordionButtons = document.querySelectorAll(".accordion__button");
    if (accordionButtons.length) {
        accordionButtons.forEach((button) => {
            button.addEventListener("click", () => {
                const accordionContent = button.nextElementSibling;
                const isActive = button.classList.contains("active");

                // Закриваємо всі інші акордеони
                accordionButtons.forEach((otherButton) => {
                    if (otherButton !== button) {
                        otherButton.classList.remove("active");
                        otherButton.nextElementSibling.style.maxHeight = "0";
                    }
                });

                // Перемикаємо поточний акордеон
                button.classList.toggle("active");
                if (!isActive) {
                    accordionContent.style.maxHeight = accordionContent.scrollHeight + "px";
                } else {
                    accordionContent.style.maxHeight = "0";
                }
            });
        });
    }
}

// Обробка кнопок "Записатись" на сторінках послуг
const orderBtns = document.querySelectorAll("#orderDetailingBtn");
if (orderBtns.length) {
    orderBtns.forEach((btn) => {
        btn.addEventListener("click", function (e) {
            e.preventDefault();
            window.location.href = "../index.html#order";
        });
    });
}
